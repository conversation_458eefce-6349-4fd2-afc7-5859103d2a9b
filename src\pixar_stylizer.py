"""
Pixar style transfer module for applying cartoon-like characteristics to 3D models.
"""

import numpy as np
import trimesh
from scipy import ndimage
from typing import Dict, Any, Tu<PERSON>
from dataclasses import dataclass

from .face_3d_generator import Face3DModel
from .utils.logger import get_logger

logger = get_logger(__name__)

@dataclass
class PixarStyleParams:
    """Parameters for Pixar stylization."""
    cartoon_factor: float = 0.8
    eye_enlargement: float = 1.3
    head_proportion: float = 1.2
    feature_exaggeration: float = 1.1
    smoothing_iterations: int = 3
    color_saturation: float = 1.2
    skin_smoothness: float = 0.9

class PixarStylizer:
    """Apply Pixar-style characteristics to 3D face models."""
    
    def __init__(self, config):
        """
        Initialize the Pixar stylizer.
        
        Args:
            config: Configuration object
        """
        self.config = config
        
        # Load stylization parameters
        self.style_params = PixarStyleParams(
            cartoon_factor=config.get('pixar_style.cartoon_factor', 0.8),
            eye_enlargement=config.get('pixar_style.eye_enlargement', 1.3),
            head_proportion=config.get('pixar_style.head_proportion', 1.2),
            feature_exaggeration=config.get('pixar_style.feature_exaggeration', 1.1),
            smoothing_iterations=config.get('pixar_style.smoothing_iterations', 3),
            color_saturation=config.get('pixar_style.color_saturation', 1.2),
            skin_smoothness=config.get('pixar_style.skin_smoothness', 0.9)
        )
        
        logger.info("Pixar stylizer initialized")
    
    def stylize(self, face_model: Face3DModel, style_intensity: float = 0.8) -> Face3DModel:
        """
        Apply Pixar stylization to a 3D face model.
        
        Args:
            face_model: Input 3D face model
            style_intensity: Intensity of stylization (0.0 to 1.0)
            
        Returns:
            Stylized Face3DModel
        """
        # Create a copy of the model
        stylized_vertices = face_model.vertices.copy()
        stylized_faces = face_model.faces.copy()
        stylized_texture_coords = face_model.texture_coords.copy()
        
        # Apply stylization transformations
        stylized_vertices = self._apply_cartoon_proportions(stylized_vertices, style_intensity)
        stylized_vertices = self._enlarge_eyes(stylized_vertices, style_intensity)
        stylized_vertices = self._exaggerate_features(stylized_vertices, style_intensity)
        stylized_vertices = self._smooth_surface(stylized_vertices, stylized_faces, style_intensity)
        
        # Calculate new normals
        stylized_normals = self._calculate_normals(stylized_vertices, stylized_faces)
        
        # Create new mesh
        stylized_mesh = trimesh.Trimesh(
            vertices=stylized_vertices,
            faces=stylized_faces,
            vertex_normals=stylized_normals
        )
        
        # Update parameters
        stylized_parameters = face_model.parameters.copy()
        stylized_parameters['style_intensity'] = style_intensity
        stylized_parameters['pixar_style'] = self.style_params.__dict__
        
        return Face3DModel(
            vertices=stylized_vertices,
            faces=stylized_faces,
            texture_coords=stylized_texture_coords,
            normals=stylized_normals,
            mesh=stylized_mesh,
            parameters=stylized_parameters
        )
    
    def _apply_cartoon_proportions(self, vertices: np.ndarray, intensity: float) -> np.ndarray:
        """Apply cartoon-like proportions to the face."""
        stylized_vertices = vertices.copy()
        
        # Calculate face center
        face_center = np.mean(vertices, axis=0)
        
        # Enlarge head overall
        head_scale = 1.0 + (self.style_params.head_proportion - 1.0) * intensity
        
        # Apply non-uniform scaling
        for i, vertex in enumerate(vertices):
            # Distance from center
            offset = vertex - face_center
            
            # Apply different scaling based on position
            # Enlarge upper part of head more (forehead area)
            if vertex[1] > face_center[1]:  # Upper part
                scale_factor = head_scale * 1.1
            else:  # Lower part
                scale_factor = head_scale * 0.95
            
            stylized_vertices[i] = face_center + offset * scale_factor
        
        return stylized_vertices
    
    def _enlarge_eyes(self, vertices: np.ndarray, intensity: float) -> np.ndarray:
        """Enlarge eye regions for cartoon effect."""
        stylized_vertices = vertices.copy()
        
        # Estimate eye positions (simplified)
        face_center = np.mean(vertices, axis=0)
        eye_y = face_center[1] + 0.1  # Slightly above center
        left_eye_center = np.array([-0.3, eye_y, face_center[2] + 0.1])
        right_eye_center = np.array([0.3, eye_y, face_center[2] + 0.1])
        
        eye_enlargement = 1.0 + (self.style_params.eye_enlargement - 1.0) * intensity
        eye_radius = 0.15  # Influence radius
        
        for i, vertex in enumerate(vertices):
            # Check distance to eye centers
            dist_left = np.linalg.norm(vertex - left_eye_center)
            dist_right = np.linalg.norm(vertex - right_eye_center)
            
            # Apply enlargement if within eye region
            if dist_left < eye_radius:
                influence = 1.0 - (dist_left / eye_radius)
                direction = vertex - left_eye_center
                stylized_vertices[i] = vertex + direction * influence * (eye_enlargement - 1.0)
            
            elif dist_right < eye_radius:
                influence = 1.0 - (dist_right / eye_radius)
                direction = vertex - right_eye_center
                stylized_vertices[i] = vertex + direction * influence * (eye_enlargement - 1.0)
        
        return stylized_vertices
    
    def _exaggerate_features(self, vertices: np.ndarray, intensity: float) -> np.ndarray:
        """Exaggerate facial features for cartoon effect."""
        stylized_vertices = vertices.copy()
        
        face_center = np.mean(vertices, axis=0)
        exaggeration = 1.0 + (self.style_params.feature_exaggeration - 1.0) * intensity
        
        # Define feature regions and their exaggeration factors
        feature_regions = [
            # Nose tip
            {'center': np.array([0.0, face_center[1], face_center[2] + 0.3]), 
             'radius': 0.1, 'factor': exaggeration * 1.2},
            # Mouth region
            {'center': np.array([0.0, face_center[1] - 0.3, face_center[2] + 0.1]), 
             'radius': 0.15, 'factor': exaggeration},
            # Cheek regions
            {'center': np.array([-0.25, face_center[1] - 0.1, face_center[2]]), 
             'radius': 0.12, 'factor': exaggeration * 0.8},
            {'center': np.array([0.25, face_center[1] - 0.1, face_center[2]]), 
             'radius': 0.12, 'factor': exaggeration * 0.8},
        ]
        
        for i, vertex in enumerate(vertices):
            for region in feature_regions:
                dist = np.linalg.norm(vertex - region['center'])
                if dist < region['radius']:
                    influence = 1.0 - (dist / region['radius'])
                    direction = vertex - region['center']
                    displacement = direction * influence * (region['factor'] - 1.0)
                    stylized_vertices[i] = vertex + displacement
        
        return stylized_vertices
    
    def _smooth_surface(self, vertices: np.ndarray, faces: np.ndarray, intensity: float) -> np.ndarray:
        """Apply surface smoothing for cartoon-like appearance."""
        smoothed_vertices = vertices.copy()
        
        iterations = int(self.style_params.smoothing_iterations * intensity)
        
        for _ in range(iterations):
            new_vertices = smoothed_vertices.copy()
            
            # Laplacian smoothing
            for i, vertex in enumerate(smoothed_vertices):
                # Find neighboring vertices
                neighbors = []
                for face in faces:
                    if i in face:
                        for vertex_idx in face:
                            if vertex_idx != i and vertex_idx not in neighbors:
                                neighbors.append(vertex_idx)
                
                if neighbors:
                    # Average with neighbors
                    neighbor_positions = smoothed_vertices[neighbors]
                    avg_position = np.mean(neighbor_positions, axis=0)
                    
                    # Blend with original position
                    smoothing_factor = 0.3 * intensity
                    new_vertices[i] = vertex * (1 - smoothing_factor) + avg_position * smoothing_factor
            
            smoothed_vertices = new_vertices
        
        return smoothed_vertices
    
    def _calculate_normals(self, vertices: np.ndarray, faces: np.ndarray) -> np.ndarray:
        """Calculate vertex normals for the stylized mesh."""
        normals = np.zeros_like(vertices)
        
        # Calculate face normals and accumulate to vertices
        for face in faces:
            if len(face) >= 3:
                v0, v1, v2 = vertices[face[:3]]
                
                # Calculate face normal
                edge1 = v1 - v0
                edge2 = v2 - v0
                face_normal = np.cross(edge1, edge2)
                
                # Normalize
                norm = np.linalg.norm(face_normal)
                if norm > 0:
                    face_normal /= norm
                
                # Add to vertex normals
                for vertex_idx in face[:3]:
                    normals[vertex_idx] += face_normal
        
        # Normalize vertex normals
        for i in range(len(normals)):
            norm = np.linalg.norm(normals[i])
            if norm > 0:
                normals[i] /= norm
        
        return normals
    
    def apply_pixar_materials(self, mesh: trimesh.Trimesh) -> trimesh.Trimesh:
        """Apply Pixar-style materials and colors to the mesh."""
        # Create a copy of the mesh
        styled_mesh = mesh.copy()
        
        # Apply cartoon-like colors
        if hasattr(styled_mesh.visual, 'vertex_colors'):
            colors = styled_mesh.visual.vertex_colors
        else:
            # Create default skin-like colors
            colors = np.full((len(styled_mesh.vertices), 4), [255, 220, 177, 255], dtype=np.uint8)
        
        # Enhance saturation
        for i in range(len(colors)):
            # Convert to HSV for saturation adjustment
            r, g, b = colors[i][:3] / 255.0
            
            # Simple saturation enhancement
            max_val = max(r, g, b)
            min_val = min(r, g, b)
            
            if max_val > min_val:
                saturation_boost = self.style_params.color_saturation
                mid_val = (max_val + min_val) / 2
                
                # Enhance saturation
                r = mid_val + (r - mid_val) * saturation_boost
                g = mid_val + (g - mid_val) * saturation_boost
                b = mid_val + (b - mid_val) * saturation_boost
                
                # Clamp values
                r = max(0, min(1, r))
                g = max(0, min(1, g))
                b = max(0, min(1, b))
                
                colors[i][:3] = [int(r * 255), int(g * 255), int(b * 255)]
        
        styled_mesh.visual.vertex_colors = colors
        
        return styled_mesh
