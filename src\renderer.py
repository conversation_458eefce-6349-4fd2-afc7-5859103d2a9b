"""
Rendering module for creating final Pixar-style character images.
"""

import cv2
import numpy as np
import trimesh
from typing import Tu<PERSON>, Dict, Any, Optional
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from matplotlib.colors import LightSource

from .face_3d_generator import Face3DModel
from .clothing_pose_extractor import ClothingPoseData
from .background_processor import BackgroundData
from .utils.logger import get_logger

logger = get_logger(__name__)

class PixarRenderer:
    """Render 3D Pixar-style characters with backgrounds."""
    
    def __init__(self, config):
        """
        Initialize the Pixar renderer.
        
        Args:
            config: Configuration object
        """
        self.config = config
        self.output_width = config.get('rendering.output_width', 1920)
        self.output_height = config.get('rendering.output_height', 1080)
        self.quality = config.get('rendering.quality', 'high')
        self.lighting_setup = config.get('rendering.lighting_setup', 'three_point')
        self.camera_angle = config.get('rendering.camera_angle', 'portrait')
        self.anti_aliasing = config.get('rendering.anti_aliasing', True)
        self.samples = config.get('rendering.samples', 128)
        
        logger.info("Pixar renderer initialized")
    
    def render(self, 
               face_model: Face3DModel, 
               clothing_pose_data: ClothingPoseData, 
               background_data: BackgroundData,
               output_size: Tuple[int, int] = None) -> np.ndarray:
        """
        Render the final Pixar-style character image.
        
        Args:
            face_model: 3D face model
            clothing_pose_data: Clothing and pose information
            background_data: Background data
            output_size: Output image size (width, height)
            
        Returns:
            Rendered image as numpy array
        """
        if output_size is None:
            output_size = (self.output_width, self.output_height)
        
        width, height = output_size
        
        # Create base canvas
        canvas = np.zeros((height, width, 3), dtype=np.uint8)
        
        # Render background
        background_layer = self._render_background(background_data, output_size)
        
        # Render 3D character
        character_layer, character_mask = self._render_character(
            face_model, clothing_pose_data, output_size
        )
        
        # Composite layers
        final_image = self._composite_layers(
            background_layer, character_layer, character_mask
        )
        
        # Apply post-processing
        final_image = self._apply_post_processing(final_image)
        
        return final_image
    
    def _render_background(self, background_data: BackgroundData, output_size: Tuple[int, int]) -> np.ndarray:
        """Render the background layer."""
        width, height = output_size
        
        if background_data.stylized_background is not None:
            # Resize background to fit output
            background = cv2.resize(
                background_data.stylized_background, 
                (width, height), 
                interpolation=cv2.INTER_CUBIC
            )
        else:
            # Create default Pixar-style background
            background = self._create_default_background(width, height, background_data)
        
        return background
    
    def _create_default_background(self, width: int, height: int, background_data: BackgroundData) -> np.ndarray:
        """Create a default Pixar-style background."""
        # Use dominant color from background data
        if background_data.dominant_colors and 'dominant' in background_data.dominant_colors:
            base_color = background_data.dominant_colors['dominant']
        else:
            base_color = (135, 206, 235)  # Sky blue
        
        # Create gradient background
        background = np.zeros((height, width, 3), dtype=np.uint8)
        
        for y in range(height):
            # Create vertical gradient
            ratio = y / height
            
            # Lighten towards bottom
            r = int(base_color[0] + (255 - base_color[0]) * ratio * 0.3)
            g = int(base_color[1] + (255 - base_color[1]) * ratio * 0.3)
            b = int(base_color[2] + (255 - base_color[2]) * ratio * 0.2)
            
            background[y, :] = [min(255, r), min(255, g), min(255, b)]
        
        return background
    
    def _render_character(self, face_model: Face3DModel, clothing_pose_data: ClothingPoseData, 
                         output_size: Tuple[int, int]) -> Tuple[np.ndarray, np.ndarray]:
        """Render the 3D character."""
        width, height = output_size
        
        # Create character canvas
        character_image = np.zeros((height, width, 3), dtype=np.uint8)
        character_mask = np.zeros((height, width), dtype=np.uint8)
        
        # Set up 3D rendering parameters
        mesh = face_model.mesh
        
        # Position and scale the mesh for rendering
        positioned_mesh = self._position_mesh_for_rendering(mesh, clothing_pose_data, output_size)
        
        # Render using matplotlib 3D (simplified approach)
        rendered_face = self._render_mesh_matplotlib(positioned_mesh, output_size)
        
        # Add clothing overlay
        clothing_overlay = self._render_clothing(clothing_pose_data, output_size)
        
        # Combine face and clothing
        character_image = self._combine_face_clothing(rendered_face, clothing_overlay)
        
        # Create mask for compositing
        character_mask = self._create_character_mask(character_image)
        
        return character_image, character_mask
    
    def _position_mesh_for_rendering(self, mesh: trimesh.Trimesh, clothing_pose_data: ClothingPoseData, 
                                   output_size: Tuple[int, int]) -> trimesh.Trimesh:
        """Position and scale mesh for optimal rendering."""
        positioned_mesh = mesh.copy()
        
        # Scale mesh to appropriate size
        bounds = positioned_mesh.bounds
        mesh_size = np.max(bounds[1] - bounds[0])
        target_size = min(output_size) * 0.4  # 40% of smaller dimension
        scale_factor = target_size / mesh_size
        
        positioned_mesh.apply_scale(scale_factor)
        
        # Center the mesh
        positioned_mesh.apply_translation(-positioned_mesh.centroid)
        
        # Apply pose adjustments
        if clothing_pose_data.pose.shoulder_angle != 0:
            # Apply shoulder rotation
            angle_rad = np.radians(clothing_pose_data.pose.shoulder_angle)
            rotation_matrix = trimesh.transformations.rotation_matrix(angle_rad, [0, 0, 1])
            positioned_mesh.apply_transform(rotation_matrix)
        
        # Position for portrait view
        positioned_mesh.apply_translation([0, 0, 2])  # Move away from camera
        
        return positioned_mesh
    
    def _render_mesh_matplotlib(self, mesh: trimesh.Trimesh, output_size: Tuple[int, int]) -> np.ndarray:
        """Render mesh using matplotlib (simplified 3D rendering)."""
        width, height = output_size
        
        # Create figure
        fig = plt.figure(figsize=(width/100, height/100), dpi=100)
        ax = fig.add_subplot(111, projection='3d')
        
        # Set up lighting
        light = LightSource(azdeg=315, altdeg=45)
        
        # Get mesh data
        vertices = mesh.vertices
        faces = mesh.faces
        
        # Create face colors (Pixar-style skin tone)
        face_colors = np.full((len(faces), 4), [1.0, 0.85, 0.7, 1.0])  # Skin color
        
        # Plot the mesh
        ax.plot_trisurf(
            vertices[:, 0], vertices[:, 1], vertices[:, 2],
            triangles=faces,
            facecolors=face_colors,
            shade=True,
            alpha=0.9
        )
        
        # Set viewing angle
        ax.view_init(elev=10, azim=0)  # Portrait view
        
        # Remove axes and background
        ax.set_axis_off()
        ax.set_facecolor('none')
        
        # Set equal aspect ratio
        max_range = np.array([vertices[:, 0].max() - vertices[:, 0].min(),
                             vertices[:, 1].max() - vertices[:, 1].min(),
                             vertices[:, 2].max() - vertices[:, 2].min()]).max() / 2.0
        
        mid_x = (vertices[:, 0].max() + vertices[:, 0].min()) * 0.5
        mid_y = (vertices[:, 1].max() + vertices[:, 1].min()) * 0.5
        mid_z = (vertices[:, 2].max() + vertices[:, 2].min()) * 0.5
        
        ax.set_xlim(mid_x - max_range, mid_x + max_range)
        ax.set_ylim(mid_y - max_range, mid_y + max_range)
        ax.set_zlim(mid_z - max_range, mid_z + max_range)
        
        # Render to numpy array
        fig.canvas.draw()
        buf = np.frombuffer(fig.canvas.tostring_rgb(), dtype=np.uint8)
        buf = buf.reshape(fig.canvas.get_width_height()[::-1] + (3,))
        
        plt.close(fig)
        
        # Resize to target size
        rendered = cv2.resize(buf, (width, height), interpolation=cv2.INTER_CUBIC)
        
        return rendered
    
    def _render_clothing(self, clothing_pose_data: ClothingPoseData, output_size: Tuple[int, int]) -> np.ndarray:
        """Render clothing overlay."""
        width, height = output_size
        clothing_overlay = np.zeros((height, width, 3), dtype=np.uint8)
        
        # Get clothing mask and colors
        clothing_data = clothing_pose_data.clothing
        
        if clothing_data.clothing_mask is not None:
            # Resize clothing mask to output size
            mask_resized = cv2.resize(
                clothing_data.clothing_mask, 
                (width, height), 
                interpolation=cv2.INTER_NEAREST
            )
            
            # Apply clothing colors
            if 'primary' in clothing_data.clothing_colors:
                color = clothing_data.clothing_colors['primary']
                clothing_overlay[mask_resized > 0] = color
        
        return clothing_overlay
    
    def _combine_face_clothing(self, face_image: np.ndarray, clothing_overlay: np.ndarray) -> np.ndarray:
        """Combine face rendering with clothing overlay."""
        # Simple alpha blending
        # In a more sophisticated implementation, this would handle proper 3D occlusion
        
        combined = face_image.copy()
        
        # Add clothing where it doesn't overlap with face
        clothing_mask = np.any(clothing_overlay > 0, axis=2)
        face_mask = np.any(face_image > 0, axis=2)
        
        # Only add clothing where there's no face
        clothing_only_mask = clothing_mask & ~face_mask
        combined[clothing_only_mask] = clothing_overlay[clothing_only_mask]
        
        return combined
    
    def _create_character_mask(self, character_image: np.ndarray) -> np.ndarray:
        """Create mask for character compositing."""
        # Create mask based on non-black pixels
        mask = np.any(character_image > 10, axis=2).astype(np.uint8) * 255
        
        # Apply morphological operations to clean up mask
        kernel = np.ones((3, 3), np.uint8)
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
        
        return mask
    
    def _composite_layers(self, background: np.ndarray, character: np.ndarray, 
                         character_mask: np.ndarray) -> np.ndarray:
        """Composite background and character layers."""
        # Normalize mask to 0-1 range
        mask_normalized = character_mask.astype(np.float32) / 255.0
        
        # Expand mask to 3 channels
        mask_3d = np.stack([mask_normalized] * 3, axis=2)
        
        # Alpha blend
        composited = background.astype(np.float32) * (1 - mask_3d) + character.astype(np.float32) * mask_3d
        
        return composited.astype(np.uint8)
    
    def _apply_post_processing(self, image: np.ndarray) -> np.ndarray:
        """Apply final post-processing effects."""
        processed = image.copy()
        
        # Apply slight cartoon effect
        if self.quality == 'high':
            # Bilateral filter for cartoon look
            processed = cv2.bilateralFilter(processed, 9, 75, 75)
        
        # Enhance colors slightly
        processed = self._enhance_pixar_colors(processed)
        
        # Apply anti-aliasing if enabled
        if self.anti_aliasing:
            processed = cv2.GaussianBlur(processed, (1, 1), 0)
        
        return processed
    
    def _enhance_pixar_colors(self, image: np.ndarray) -> np.ndarray:
        """Enhance colors for Pixar-style appearance."""
        enhanced = image.copy().astype(np.float32)
        
        # Convert to HSV for color manipulation
        hsv = cv2.cvtColor(enhanced / 255.0, cv2.COLOR_RGB2HSV)
        
        # Slightly increase saturation
        hsv[:, :, 1] *= 1.1
        hsv[:, :, 1] = np.clip(hsv[:, :, 1], 0, 1)
        
        # Slightly increase brightness
        hsv[:, :, 2] *= 1.05
        hsv[:, :, 2] = np.clip(hsv[:, :, 2], 0, 1)
        
        # Convert back to RGB
        enhanced = cv2.cvtColor(hsv, cv2.COLOR_HSV2RGB) * 255
        enhanced = np.clip(enhanced, 0, 255).astype(np.uint8)
        
        return enhanced
