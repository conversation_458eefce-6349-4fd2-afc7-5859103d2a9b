"""
Background processing module for extracting and stylizing backgrounds.
"""

import cv2
import numpy as np
import mediapipe as mp
from typing import Dict, <PERSON>, <PERSON><PERSON>, Optional
from dataclasses import dataclass
from scipy import ndimage

from .face_analyzer import FaceData
from .utils.logger import get_logger

logger = get_logger(__name__)

@dataclass
class BackgroundData:
    """Container for background information."""
    background_image: np.ndarray  # Extracted background
    background_mask: np.ndarray   # Background segmentation mask
    stylized_background: np.ndarray  # Pixar-stylized background
    dominant_colors: Dict[str, Tuple[int, int, int]]  # Background colors
    lighting_info: Dict[str, Any]  # Lighting analysis
    depth_map: Optional[np.ndarray] = None  # Estimated depth

class BackgroundProcessor:
    """Process and stylize backgrounds for Pixar-style rendering."""
    
    def __init__(self, config):
        """
        Initialize the background processor.
        
        Args:
            config: Configuration object
        """
        self.config = config
        self.segmentation_threshold = config.get('background.segmentation_threshold', 0.8)
        self.blur_background = config.get('background.blur_background', False)
        self.stylize_background = config.get('background.stylize_background', True)
        self.style_intensity = config.get('background.background_style_intensity', 0.6)
        
        # Initialize MediaPipe Selfie Segmentation
        self.mp_selfie_segmentation = mp.solutions.selfie_segmentation
        self.selfie_segmentation = self.mp_selfie_segmentation.SelfieSegmentation(
            model_selection=1
        )
        
        logger.info("Background processor initialized")
    
    def process(self, image: np.ndarray, face_data: FaceData) -> BackgroundData:
        """
        Process background from input image.
        
        Args:
            image: Input image as numpy array
            face_data: Face analysis data for context
            
        Returns:
            BackgroundData object
        """
        # Convert BGR to RGB if needed
        if len(image.shape) == 3 and image.shape[2] == 3:
            rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        else:
            rgb_image = image
        
        # Extract background
        background_image, background_mask = self._extract_background(rgb_image)
        
        # Analyze background properties
        dominant_colors = self._analyze_background_colors(background_image, background_mask)
        lighting_info = self._analyze_lighting(background_image, background_mask)
        
        # Estimate depth (simplified)
        depth_map = self._estimate_depth(background_image)
        
        # Stylize background
        stylized_background = self._stylize_background(background_image, background_mask)
        
        return BackgroundData(
            background_image=background_image,
            background_mask=background_mask,
            stylized_background=stylized_background,
            dominant_colors=dominant_colors,
            lighting_info=lighting_info,
            depth_map=depth_map
        )
    
    def _extract_background(self, image: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Extract background from image using segmentation."""
        # Get person segmentation
        results = self.selfie_segmentation.process(image)
        person_mask = results.segmentation_mask
        
        # Create background mask (inverse of person mask)
        background_mask = (person_mask < self.segmentation_threshold).astype(np.uint8)
        
        # Refine mask using morphological operations
        kernel = np.ones((5, 5), np.uint8)
        background_mask = cv2.morphologyEx(background_mask, cv2.MORPH_CLOSE, kernel)
        background_mask = cv2.morphologyEx(background_mask, cv2.MORPH_OPEN, kernel)
        
        # Extract background
        background_image = image.copy()
        background_image[background_mask == 0] = 0  # Set person pixels to black
        
        return background_image, background_mask
    
    def _analyze_background_colors(self, background_image: np.ndarray, background_mask: np.ndarray) -> Dict[str, Tuple[int, int, int]]:
        """Analyze dominant colors in the background."""
        colors = {}
        
        # Get background pixels
        background_pixels = background_image[background_mask > 0]
        
        if len(background_pixels) > 0:
            # Calculate dominant colors
            mean_color = np.mean(background_pixels, axis=0).astype(int)
            colors['dominant'] = tuple(mean_color)
            
            # Calculate secondary color using median
            median_color = np.median(background_pixels, axis=0).astype(int)
            colors['secondary'] = tuple(median_color)
            
            # Calculate brightest and darkest colors
            brightness = np.sum(background_pixels, axis=1)
            brightest_idx = np.argmax(brightness)
            darkest_idx = np.argmin(brightness)
            
            colors['brightest'] = tuple(background_pixels[brightest_idx].astype(int))
            colors['darkest'] = tuple(background_pixels[darkest_idx].astype(int))
        else:
            # Default colors
            colors['dominant'] = (128, 128, 128)
            colors['secondary'] = (100, 100, 100)
            colors['brightest'] = (200, 200, 200)
            colors['darkest'] = (50, 50, 50)
        
        return colors
    
    def _analyze_lighting(self, background_image: np.ndarray, background_mask: np.ndarray) -> Dict[str, Any]:
        """Analyze lighting conditions in the background."""
        lighting_info = {}
        
        # Convert to grayscale for analysis
        gray_background = cv2.cvtColor(background_image, cv2.COLOR_RGB2GRAY)
        background_pixels = gray_background[background_mask > 0]
        
        if len(background_pixels) > 0:
            # Calculate lighting statistics
            lighting_info['mean_brightness'] = float(np.mean(background_pixels))
            lighting_info['brightness_std'] = float(np.std(background_pixels))
            lighting_info['min_brightness'] = float(np.min(background_pixels))
            lighting_info['max_brightness'] = float(np.max(background_pixels))
            
            # Estimate lighting direction (simplified)
            h, w = gray_background.shape
            
            # Divide image into quadrants and compare brightness
            quad_brightness = []
            for i in range(2):
                for j in range(2):
                    y_start = i * h // 2
                    y_end = (i + 1) * h // 2
                    x_start = j * w // 2
                    x_end = (j + 1) * w // 2
                    
                    quad_mask = background_mask[y_start:y_end, x_start:x_end]
                    quad_pixels = gray_background[y_start:y_end, x_start:x_end][quad_mask > 0]
                    
                    if len(quad_pixels) > 0:
                        quad_brightness.append(np.mean(quad_pixels))
                    else:
                        quad_brightness.append(128)  # Default
            
            # Determine lighting direction
            max_quad = np.argmax(quad_brightness)
            lighting_directions = ['top_left', 'top_right', 'bottom_left', 'bottom_right']
            lighting_info['primary_light_direction'] = lighting_directions[max_quad]
            
            # Calculate contrast
            lighting_info['contrast'] = float(np.max(background_pixels) - np.min(background_pixels))
        else:
            # Default lighting info
            lighting_info['mean_brightness'] = 128.0
            lighting_info['brightness_std'] = 30.0
            lighting_info['min_brightness'] = 50.0
            lighting_info['max_brightness'] = 200.0
            lighting_info['primary_light_direction'] = 'top_left'
            lighting_info['contrast'] = 150.0
        
        return lighting_info
    
    def _estimate_depth(self, background_image: np.ndarray) -> np.ndarray:
        """Estimate depth map for the background (simplified)."""
        # Convert to grayscale
        gray = cv2.cvtColor(background_image, cv2.COLOR_RGB2GRAY)
        
        # Use blur as a simple depth cue (blurrier = farther)
        # Calculate local variance as a measure of sharpness
        kernel = np.ones((9, 9), np.float32) / 81
        mean = cv2.filter2D(gray.astype(np.float32), -1, kernel)
        sqr_mean = cv2.filter2D((gray.astype(np.float32))**2, -1, kernel)
        variance = sqr_mean - mean**2
        
        # Invert variance to get depth (low variance = far, high variance = near)
        depth_map = 255 - cv2.normalize(variance, None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)
        
        # Apply Gaussian smoothing
        depth_map = cv2.GaussianBlur(depth_map, (15, 15), 0)
        
        return depth_map
    
    def _stylize_background(self, background_image: np.ndarray, background_mask: np.ndarray) -> np.ndarray:
        """Apply Pixar-style stylization to the background."""
        if not self.stylize_background:
            return background_image
        
        stylized = background_image.copy()
        
        # Apply cartoon-like effects
        stylized = self._apply_cartoon_filter(stylized, background_mask)
        stylized = self._enhance_colors(stylized, background_mask)
        stylized = self._smooth_background(stylized, background_mask)
        
        if self.blur_background:
            stylized = self._apply_depth_blur(stylized, background_mask)
        
        return stylized
    
    def _apply_cartoon_filter(self, image: np.ndarray, mask: np.ndarray) -> np.ndarray:
        """Apply cartoon-like filter to background."""
        # Bilateral filter for cartoon effect
        cartoon = cv2.bilateralFilter(image, 15, 80, 80)
        
        # Edge detection and enhancement
        gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        edges = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_MEAN_C, cv2.THRESH_BINARY, 7, 7)
        edges = cv2.cvtColor(edges, cv2.COLOR_GRAY2RGB)
        
        # Combine cartoon effect with edges
        cartoon = cv2.bitwise_and(cartoon, edges)
        
        # Apply only to background regions
        result = image.copy()
        result[mask > 0] = cartoon[mask > 0]
        
        return result
    
    def _enhance_colors(self, image: np.ndarray, mask: np.ndarray) -> np.ndarray:
        """Enhance colors for Pixar-style appearance."""
        enhanced = image.copy().astype(np.float32)
        
        # Increase saturation
        hsv = cv2.cvtColor(enhanced / 255.0, cv2.COLOR_RGB2HSV)
        hsv[:, :, 1] *= 1.3  # Increase saturation
        hsv[:, :, 1] = np.clip(hsv[:, :, 1], 0, 1)
        
        # Slightly increase brightness
        hsv[:, :, 2] *= 1.1
        hsv[:, :, 2] = np.clip(hsv[:, :, 2], 0, 1)
        
        enhanced = cv2.cvtColor(hsv, cv2.COLOR_HSV2RGB) * 255
        enhanced = np.clip(enhanced, 0, 255).astype(np.uint8)
        
        # Apply only to background regions
        result = image.copy()
        result[mask > 0] = enhanced[mask > 0]
        
        return result
    
    def _smooth_background(self, image: np.ndarray, mask: np.ndarray) -> np.ndarray:
        """Apply smoothing to background for cartoon effect."""
        # Apply Gaussian blur for smoothing
        smoothed = cv2.GaussianBlur(image, (5, 5), 0)
        
        # Apply only to background regions
        result = image.copy()
        result[mask > 0] = smoothed[mask > 0]
        
        return result
    
    def _apply_depth_blur(self, image: np.ndarray, mask: np.ndarray) -> np.ndarray:
        """Apply depth-based blur to background."""
        if hasattr(self, 'depth_map') and self.depth_map is not None:
            # Use depth map to apply variable blur
            blurred = image.copy()
            
            # Apply different blur levels based on depth
            for blur_level in range(1, 6):
                depth_threshold = blur_level * 50
                depth_mask = (self.depth_map > depth_threshold) & (mask > 0)
                
                if np.any(depth_mask):
                    kernel_size = blur_level * 2 + 1
                    region_blurred = cv2.GaussianBlur(image, (kernel_size, kernel_size), 0)
                    blurred[depth_mask] = region_blurred[depth_mask]
            
            return blurred
        else:
            # Simple uniform blur
            return cv2.GaussianBlur(image, (7, 7), 0)
    
    def create_pixar_sky(self, width: int, height: int, base_color: Tuple[int, int, int] = (135, 206, 235)) -> np.ndarray:
        """Create a Pixar-style sky background."""
        # Create gradient sky
        sky = np.zeros((height, width, 3), dtype=np.uint8)
        
        # Create vertical gradient
        for y in range(height):
            # Gradient from light blue at top to white at bottom
            ratio = y / height
            
            # Sky blue to white gradient
            r = int(base_color[0] + (255 - base_color[0]) * ratio)
            g = int(base_color[1] + (255 - base_color[1]) * ratio)
            b = int(base_color[2] + (255 - base_color[2]) * ratio * 0.5)
            
            sky[y, :] = [r, g, b]
        
        # Add some cartoon-style clouds (optional)
        # This could be expanded with more sophisticated cloud generation
        
        return sky
