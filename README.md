# Pixar Style 3D Character Generator

Transform face images into stunning 3D Pixar-style character portraits while maintaining the original clothing, pose, and background!

## Features

- **Face-to-3D Conversion**: Advanced facial analysis and 3D model generation
- **Pixar Stylization**: Apply cartoon-like characteristics with enlarged eyes, smooth surfaces, and exaggerated features
- **Clothing Preservation**: Detect and maintain original clothing details and colors
- **Pose Retention**: Preserve the original pose and body orientation
- **Background Stylization**: Convert backgrounds to Pixar-style while maintaining the original scene
- **High-Quality Output**: Generate 1920x1080 landscape format images
- **Interactive Interface**: User-friendly web interface with adjustable style parameters

## Installation

1. **Clone the repository**:
```bash
git clone <repository-url>
cd Pixar
```

2. **Install dependencies**:
```bash
pip install -r requirements.txt
```

3. **Download required models** (optional - models will be downloaded automatically on first use):
```bash
python -c "import mediapipe as mp; mp.solutions.face_mesh.FaceMesh()"
```

## Quick Start

### Using the Web Interface

1. **Launch the application**:
```bash
python main.py
```

2. **Open your browser** and navigate to `http://localhost:7860`

3. **Upload a face image** and adjust the style intensity

4. **Click "Generate Character"** and wait for the magic to happen!

### Using the Command Line

```python
from src.face_analyzer import FaceAnalyzer
from src.face_3d_generator import Face3DGenerator
from src.pixar_stylizer import PixarStylizer
from src.clothing_pose_extractor import ClothingPoseExtractor
from src.background_processor import BackgroundProcessor
from src.renderer import PixarRenderer
from src.utils.config import Config
import cv2

# Load configuration
config = Config("config/default.yaml")

# Initialize components
face_analyzer = FaceAnalyzer(config)
face_3d_generator = Face3DGenerator(config)
pixar_stylizer = PixarStylizer(config)
clothing_extractor = ClothingPoseExtractor(config)
background_processor = BackgroundProcessor(config)
renderer = PixarRenderer(config)

# Load and process image
image = cv2.imread("path/to/your/image.jpg")
image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

# Generate character
face_data = face_analyzer.analyze(image)
face_3d_model = face_3d_generator.generate(face_data)
stylized_model = pixar_stylizer.stylize(face_3d_model, style_intensity=0.8)
clothing_pose_data = clothing_extractor.extract(image, face_data)
background_data = background_processor.process(image, face_data)

# Render final image
final_image = renderer.render(stylized_model, clothing_pose_data, background_data)

# Save result
cv2.imwrite("output/pixar_character.jpg", cv2.cvtColor(final_image, cv2.COLOR_RGB2BGR))
```

## Configuration

Customize the generation process by editing `config/default.yaml`:

```yaml
# Pixar stylization settings
pixar_style:
  cartoon_factor: 0.8          # Overall cartoon intensity
  eye_enlargement: 1.3         # Eye size multiplier
  head_proportion: 1.2         # Head size adjustment
  feature_exaggeration: 1.1    # Feature enhancement
  smoothing_iterations: 3      # Surface smoothing
  color_saturation: 1.2        # Color enhancement
  skin_smoothness: 0.9         # Skin texture smoothing

# Rendering settings
rendering:
  output_width: 1920           # Output image width
  output_height: 1080          # Output image height
  quality: "high"              # Rendering quality
  lighting_setup: "three_point" # Lighting configuration
```

## Project Structure

```
Pixar/
├── main.py                 # Main application entry point
├── requirements.txt        # Python dependencies
├── config/
│   └── default.yaml       # Configuration file
├── src/
│   ├── face_analyzer.py           # Face detection and analysis
│   ├── face_3d_generator.py       # 3D face model generation
│   ├── pixar_stylizer.py          # Pixar style application
│   ├── clothing_pose_extractor.py # Clothing and pose extraction
│   ├── background_processor.py    # Background processing
│   ├── renderer.py               # Final rendering pipeline
│   └── utils/
│       ├── config.py             # Configuration management
│       └── logger.py             # Logging utilities
├── examples/              # Example images
└── output/               # Generated results
```

## How It Works

1. **Face Analysis**: Uses MediaPipe to detect facial landmarks and analyze facial features
2. **3D Generation**: Converts 2D facial features into a 3D mesh using FLAME-inspired modeling
3. **Pixar Stylization**: Applies cartoon characteristics like enlarged eyes, smooth surfaces, and exaggerated proportions
4. **Clothing Detection**: Extracts clothing information using segmentation and pose estimation
5. **Background Processing**: Separates and stylizes the background while preserving the original scene
6. **Final Rendering**: Combines all elements into a high-quality 1920x1080 Pixar-style portrait

## Examples

### Input vs Output
- **Input**: Regular face photo
- **Output**: 3D Pixar-style character with:
  - Cartoon-like facial features
  - Preserved clothing and pose
  - Stylized background
  - Professional 16:9 composition

## Advanced Usage

### Custom Style Parameters

```python
# Create custom stylization
custom_params = {
    'cartoon_factor': 0.9,
    'eye_enlargement': 1.5,
    'head_proportion': 1.3
}

# Apply custom style
stylized_model = pixar_stylizer.stylize(face_3d_model, style_intensity=0.8)
```

### Batch Processing

```python
import os
from pathlib import Path

input_dir = "input_images/"
output_dir = "output_images/"

for image_file in Path(input_dir).glob("*.jpg"):
    # Process each image
    image = cv2.imread(str(image_file))
    # ... processing code ...
    output_path = Path(output_dir) / f"pixar_{image_file.name}"
    cv2.imwrite(str(output_path), final_image)
```

## Requirements

- Python 3.8+
- OpenCV
- MediaPipe
- NumPy
- Trimesh
- PyTorch (for advanced features)
- Gradio (for web interface)

## Troubleshooting

### Common Issues

1. **Face not detected**: Ensure the face is clearly visible and well-lit
2. **Poor quality output**: Try adjusting the style intensity or image resolution
3. **Memory issues**: Reduce batch size or image resolution in config
4. **Slow processing**: Consider using GPU acceleration if available

### Performance Tips

- Use GPU acceleration when available
- Reduce output resolution for faster processing
- Adjust quality settings based on your needs
- Process images in batches for efficiency

## Contributing

We welcome contributions! Please see our contributing guidelines for more information.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- MediaPipe for face detection and pose estimation
- FLAME model inspiration for 3D face generation
- Pixar Animation Studios for artistic inspiration
- Open source computer vision community
