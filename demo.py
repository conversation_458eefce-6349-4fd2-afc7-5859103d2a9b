#!/usr/bin/env python3
"""
Demo script for the Pixar Character Generator.
This script demonstrates how to use the generator programmatically.
"""

import os
import sys
import cv2
import numpy as np
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.face_analyzer import Face<PERSON><PERSON>y<PERSON>
from src.face_3d_generator import Face3<PERSON><PERSON>enerator
from src.pixar_stylizer import PixarStylizer
from src.clothing_pose_extractor import ClothingPoseExtractor
from src.background_processor import BackgroundProcessor
from src.renderer import PixarRenderer
from src.utils.config import Config
from src.utils.logger import setup_logger

def create_sample_image():
    """Create a sample face image for demonstration."""
    # Create a simple face-like image for testing
    height, width = 480, 640
    image = np.zeros((height, width, 3), dtype=np.uint8)
    
    # Background
    image[:] = (135, 206, 235)  # Sky blue
    
    # Face (ellipse)
    face_center = (width // 2, height // 2)
    face_axes = (80, 100)
    cv2.ellipse(image, face_center, face_axes, 0, 0, 360, (255, 220, 177), -1)
    
    # Eyes
    left_eye = (face_center[0] - 25, face_center[1] - 20)
    right_eye = (face_center[0] + 25, face_center[1] - 20)
    cv2.circle(image, left_eye, 8, (0, 0, 0), -1)
    cv2.circle(image, right_eye, 8, (0, 0, 0), -1)
    
    # Nose
    nose_points = np.array([
        [face_center[0], face_center[1] - 5],
        [face_center[0] - 5, face_center[1] + 10],
        [face_center[0] + 5, face_center[1] + 10]
    ], np.int32)
    cv2.fillPoly(image, [nose_points], (200, 180, 140))
    
    # Mouth
    mouth_center = (face_center[0], face_center[1] + 25)
    cv2.ellipse(image, mouth_center, (15, 8), 0, 0, 180, (150, 50, 50), 2)
    
    # Simple clothing (rectangle)
    clothing_rect = (
        face_center[0] - 100, face_center[1] + 80,
        200, 150
    )
    cv2.rectangle(image, 
                 (clothing_rect[0], clothing_rect[1]),
                 (clothing_rect[0] + clothing_rect[2], clothing_rect[1] + clothing_rect[3]),
                 (100, 150, 200), -1)
    
    return image

def demo_basic_usage():
    """Demonstrate basic usage of the Pixar Character Generator."""
    print("🎭 Pixar Character Generator Demo")
    print("=" * 50)
    
    # Setup logging
    logger = setup_logger(__name__)
    
    try:
        # Load configuration
        config_path = "config/default.yaml"
        if not os.path.exists(config_path):
            print(f"❌ Configuration file not found: {config_path}")
            print("Please make sure you're running from the project root directory.")
            return
        
        config = Config(config_path)
        logger.info("Configuration loaded successfully")
        
        # Initialize components
        print("🔧 Initializing components...")
        face_analyzer = FaceAnalyzer(config)
        face_3d_generator = Face3DGenerator(config)
        pixar_stylizer = PixarStylizer(config)
        clothing_extractor = ClothingPoseExtractor(config)
        background_processor = BackgroundProcessor(config)
        renderer = PixarRenderer(config)
        
        print("✅ All components initialized successfully!")
        
        # Create or load sample image
        sample_image_path = "examples/sample_face.jpg"
        
        if os.path.exists(sample_image_path):
            print(f"📸 Loading sample image: {sample_image_path}")
            image = cv2.imread(sample_image_path)
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        else:
            print("📸 Creating sample image for demonstration...")
            image = create_sample_image()
            
            # Save sample image
            os.makedirs("examples", exist_ok=True)
            cv2.imwrite(sample_image_path, cv2.cvtColor(image, cv2.COLOR_RGB2BGR))
            print(f"💾 Sample image saved to: {sample_image_path}")
        
        print(f"Image shape: {image.shape}")
        
        # Process the image
        print("\n🔍 Step 1: Analyzing face...")
        try:
            face_data = face_analyzer.analyze(image)
            print(f"✅ Face detected with confidence: {face_data.confidence:.2f}")
            print(f"   Face bbox: {face_data.face_bbox}")
            print(f"   Landmarks: {len(face_data.landmarks_2d)} points")
        except Exception as e:
            print(f"❌ Face analysis failed: {e}")
            print("This might happen with the synthetic sample image.")
            print("Try using a real face photo for better results.")
            return
        
        print("\n🎯 Step 2: Generating 3D face model...")
        face_3d_model = face_3d_generator.generate(face_data)
        print(f"✅ 3D model generated with {len(face_3d_model.vertices)} vertices")
        
        print("\n🎨 Step 3: Applying Pixar stylization...")
        style_intensity = 0.8
        stylized_model = pixar_stylizer.stylize(face_3d_model, style_intensity)
        print(f"✅ Pixar style applied with intensity: {style_intensity}")
        
        print("\n👔 Step 4: Extracting clothing and pose...")
        clothing_pose_data = clothing_extractor.extract(image, face_data)
        print(f"✅ Detected clothing types: {clothing_pose_data.clothing.clothing_types}")
        print(f"   Body orientation: {clothing_pose_data.pose.body_orientation}")
        
        print("\n🌅 Step 5: Processing background...")
        background_data = background_processor.process(image, face_data)
        print(f"✅ Background processed")
        print(f"   Dominant colors: {background_data.dominant_colors}")
        
        print("\n🎬 Step 6: Rendering final image...")
        final_image = renderer.render(
            stylized_model, 
            clothing_pose_data, 
            background_data,
            output_size=(1920, 1080)
        )
        print(f"✅ Final image rendered: {final_image.shape}")
        
        # Save the result
        output_dir = "output"
        os.makedirs(output_dir, exist_ok=True)
        output_path = os.path.join(output_dir, "demo_pixar_character.jpg")
        
        cv2.imwrite(output_path, cv2.cvtColor(final_image, cv2.COLOR_RGB2BGR))
        print(f"💾 Result saved to: {output_path}")
        
        print("\n🎉 Demo completed successfully!")
        print(f"📁 Check the output directory for your Pixar-style character!")
        
    except Exception as e:
        logger.error(f"Demo failed: {str(e)}")
        print(f"❌ Demo failed: {e}")
        print("\nTroubleshooting tips:")
        print("1. Make sure all dependencies are installed: pip install -r requirements.txt")
        print("2. Check that you're running from the project root directory")
        print("3. Try using a clear face photo as input")

def demo_batch_processing():
    """Demonstrate batch processing of multiple images."""
    print("\n🔄 Batch Processing Demo")
    print("=" * 30)
    
    input_dir = "examples"
    output_dir = "output/batch"
    
    # Create directories
    os.makedirs(input_dir, exist_ok=True)
    os.makedirs(output_dir, exist_ok=True)
    
    # Find image files
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp']
    image_files = []
    
    for ext in image_extensions:
        image_files.extend(Path(input_dir).glob(f"*{ext}"))
        image_files.extend(Path(input_dir).glob(f"*{ext.upper()}"))
    
    if not image_files:
        print(f"📁 No images found in {input_dir}")
        print("Add some face images to the examples directory to try batch processing.")
        return
    
    print(f"📸 Found {len(image_files)} images to process")
    
    # Process each image
    for i, image_file in enumerate(image_files, 1):
        print(f"\n🔄 Processing {i}/{len(image_files)}: {image_file.name}")
        
        try:
            # Load image
            image = cv2.imread(str(image_file))
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # Quick processing (simplified for demo)
            # In practice, you'd use the full pipeline here
            
            # Save processed image
            output_filename = f"pixar_{image_file.stem}.jpg"
            output_path = Path(output_dir) / output_filename
            
            # For demo, just save a copy with some basic processing
            processed = cv2.bilateralFilter(image, 15, 80, 80)  # Cartoon effect
            cv2.imwrite(str(output_path), cv2.cvtColor(processed, cv2.COLOR_RGB2BGR))
            
            print(f"✅ Saved: {output_filename}")
            
        except Exception as e:
            print(f"❌ Failed to process {image_file.name}: {e}")
    
    print(f"\n🎉 Batch processing completed!")
    print(f"📁 Results saved in: {output_dir}")

if __name__ == "__main__":
    # Run basic demo
    demo_basic_usage()
    
    # Optionally run batch processing demo
    print("\n" + "=" * 50)
    response = input("Would you like to try batch processing demo? (y/n): ")
    if response.lower() in ['y', 'yes']:
        demo_batch_processing()
    
    print("\n🎭 Thank you for trying the Pixar Character Generator!")
    print("For more features, run: python main.py")
