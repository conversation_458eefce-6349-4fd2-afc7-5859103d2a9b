#!/usr/bin/env python3
"""
Setup script for the Pixar Character Generator.
"""

import os
import sys
import subprocess
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        if e.stdout:
            print(f"Output: {e.stdout}")
        if e.stderr:
            print(f"Error: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible."""
    print("🐍 Checking Python version...")
    version = sys.version_info
    
    if version.major == 3 and version.minor >= 8:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} is not compatible")
        print("Please install Python 3.8 or higher")
        return False

def install_dependencies():
    """Install required dependencies."""
    print("📦 Installing dependencies...")
    
    # Check if requirements.txt exists
    if not Path("requirements.txt").exists():
        print("❌ requirements.txt not found")
        return False
    
    # Install dependencies
    commands = [
        "pip install --upgrade pip",
        "pip install -r requirements.txt"
    ]
    
    for command in commands:
        if not run_command(command, f"Running: {command}"):
            return False
    
    return True

def create_directories():
    """Create necessary directories."""
    print("📁 Creating directories...")
    
    directories = [
        "examples",
        "output",
        "output/batch",
        "models"  # For downloaded models
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ Created directory: {directory}")
    
    return True

def download_models():
    """Download required models."""
    print("🤖 Downloading models...")
    
    try:
        # Import MediaPipe to trigger model downloads
        import mediapipe as mp
        
        # Initialize models to download them
        print("   Downloading MediaPipe Face Mesh...")
        face_mesh = mp.solutions.face_mesh.FaceMesh(
            static_image_mode=True,
            max_num_faces=1,
            refine_landmarks=True,
            min_detection_confidence=0.5
        )
        
        print("   Downloading MediaPipe Pose...")
        pose = mp.solutions.pose.Pose(
            static_image_mode=True,
            model_complexity=2,
            enable_segmentation=True,
            min_detection_confidence=0.5
        )
        
        print("   Downloading MediaPipe Selfie Segmentation...")
        selfie_seg = mp.solutions.selfie_segmentation.SelfieSegmentation(
            model_selection=1
        )
        
        print("✅ Models downloaded successfully")
        return True
        
    except Exception as e:
        print(f"❌ Model download failed: {e}")
        print("Models will be downloaded automatically on first use")
        return True  # Not critical for setup

def create_sample_files():
    """Create sample configuration and example files."""
    print("📄 Creating sample files...")
    
    # Create a simple example image if none exists
    example_dir = Path("examples")
    if not any(example_dir.glob("*.jpg")) and not any(example_dir.glob("*.png")):
        print("   Creating sample image...")
        try:
            import cv2
            import numpy as np
            
            # Create a simple test image
            height, width = 400, 400
            image = np.zeros((height, width, 3), dtype=np.uint8)
            image[:] = (240, 240, 240)  # Light gray background
            
            # Add text
            font = cv2.FONT_HERSHEY_SIMPLEX
            text = "Add your face"
            text2 = "images here"
            text_size = cv2.getTextSize(text, font, 1, 2)[0]
            text_x = (width - text_size[0]) // 2
            text_y = height // 2 - 20
            
            cv2.putText(image, text, (text_x, text_y), font, 1, (100, 100, 100), 2)
            cv2.putText(image, text2, (text_x, text_y + 40), font, 1, (100, 100, 100), 2)
            
            cv2.imwrite(str(example_dir / "placeholder.jpg"), image)
            print("✅ Created placeholder image")
            
        except Exception as e:
            print(f"⚠️  Could not create sample image: {e}")
    
    return True

def run_tests():
    """Run installation tests."""
    print("🧪 Running installation tests...")
    
    if Path("test_installation.py").exists():
        return run_command("python test_installation.py", "Running installation tests")
    else:
        print("⚠️  test_installation.py not found, skipping tests")
        return True

def main():
    """Main setup function."""
    print("🎭 Pixar Character Generator Setup")
    print("=" * 50)
    
    steps = [
        ("Check Python Version", check_python_version),
        ("Install Dependencies", install_dependencies),
        ("Create Directories", create_directories),
        ("Download Models", download_models),
        ("Create Sample Files", create_sample_files),
        ("Run Tests", run_tests)
    ]
    
    failed_steps = []
    
    for step_name, step_func in steps:
        print(f"\n📋 Step: {step_name}")
        print("-" * 30)
        
        try:
            if not step_func():
                failed_steps.append(step_name)
        except Exception as e:
            print(f"❌ {step_name} failed with exception: {e}")
            failed_steps.append(step_name)
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 SETUP SUMMARY")
    print("=" * 50)
    
    if not failed_steps:
        print("🎉 Setup completed successfully!")
        print("\nNext steps:")
        print("1. Test the installation: python test_installation.py")
        print("2. Run a quick demo: python demo.py")
        print("3. Start the web interface: python main.py")
        print("4. Add your face images to the 'examples' directory")
        
        print("\n📚 Documentation:")
        print("- README.md - Complete usage guide")
        print("- config/default.yaml - Configuration options")
        print("- examples/ - Place your input images here")
        print("- output/ - Generated results will be saved here")
        
    else:
        print(f"⚠️  Setup completed with {len(failed_steps)} issue(s):")
        for step in failed_steps:
            print(f"   - {step}")
        
        print("\n🔧 Troubleshooting:")
        print("1. Make sure you have Python 3.8+ installed")
        print("2. Try running: pip install --upgrade pip")
        print("3. Install dependencies manually: pip install -r requirements.txt")
        print("4. Check your internet connection for model downloads")
        
        print("\nYou can still try running the application:")
        print("- python test_installation.py (to check what's working)")
        print("- python demo.py (for a basic demo)")

if __name__ == "__main__":
    main()
