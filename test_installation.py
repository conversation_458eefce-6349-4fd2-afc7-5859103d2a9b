#!/usr/bin/env python3
"""
Test script to verify the Pixar Character Generator installation.
"""

import sys
import importlib
from pathlib import Path

def test_python_version():
    """Test Python version compatibility."""
    print("🐍 Testing Python version...")
    version = sys.version_info
    
    if version.major == 3 and version.minor >= 8:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} - Compatible")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} - Requires Python 3.8+")
        return False

def test_dependencies():
    """Test if required dependencies are installed."""
    print("\n📦 Testing dependencies...")
    
    required_packages = [
        'numpy',
        'opencv-python',
        'Pillow',
        'matplotlib',
        'mediapipe',
        'trimesh',
        'gradio',
        'yaml',
        'scipy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            # Handle special cases
            if package == 'opencv-python':
                import cv2
                print(f"✅ {package} (cv2) - {cv2.__version__}")
            elif package == 'Pillow':
                import PIL
                print(f"✅ {package} (PIL) - {PIL.__version__}")
            elif package == 'yaml':
                import yaml
                print(f"✅ {package} - Available")
            else:
                module = importlib.import_module(package)
                version = getattr(module, '__version__', 'Unknown')
                print(f"✅ {package} - {version}")
        except ImportError:
            print(f"❌ {package} - Not installed")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  Missing packages: {', '.join(missing_packages)}")
        print("Install with: pip install " + " ".join(missing_packages))
        return False
    else:
        print("\n✅ All dependencies are installed!")
        return True

def test_project_structure():
    """Test if project structure is correct."""
    print("\n📁 Testing project structure...")
    
    required_files = [
        'main.py',
        'requirements.txt',
        'config/default.yaml',
        'src/__init__.py',
        'src/face_analyzer.py',
        'src/face_3d_generator.py',
        'src/pixar_stylizer.py',
        'src/clothing_pose_extractor.py',
        'src/background_processor.py',
        'src/renderer.py',
        'src/utils/__init__.py',
        'src/utils/config.py',
        'src/utils/logger.py'
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - Missing")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n⚠️  Missing files: {len(missing_files)}")
        return False
    else:
        print("\n✅ Project structure is complete!")
        return True

def test_configuration():
    """Test if configuration can be loaded."""
    print("\n⚙️  Testing configuration...")
    
    try:
        sys.path.append(str(Path(__file__).parent / "src"))
        from src.utils.config import Config
        
        config = Config("config/default.yaml")
        
        # Test some key configuration values
        app_name = config.get('app.name', 'Unknown')
        face_confidence = config.get('face_analysis.detection_confidence', 0.0)
        output_width = config.get('rendering.output_width', 0)
        
        print(f"✅ Configuration loaded successfully")
        print(f"   App name: {app_name}")
        print(f"   Face detection confidence: {face_confidence}")
        print(f"   Output width: {output_width}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_imports():
    """Test if main modules can be imported."""
    print("\n🔧 Testing module imports...")
    
    modules_to_test = [
        'src.face_analyzer',
        'src.face_3d_generator',
        'src.pixar_stylizer',
        'src.clothing_pose_extractor',
        'src.background_processor',
        'src.renderer',
        'src.utils.config',
        'src.utils.logger'
    ]
    
    sys.path.append(str(Path(__file__).parent / "src"))
    
    failed_imports = []
    
    for module_name in modules_to_test:
        try:
            module = importlib.import_module(module_name)
            print(f"✅ {module_name}")
        except Exception as e:
            print(f"❌ {module_name} - {e}")
            failed_imports.append(module_name)
    
    if failed_imports:
        print(f"\n⚠️  Failed imports: {len(failed_imports)}")
        return False
    else:
        print("\n✅ All modules imported successfully!")
        return True

def test_mediapipe():
    """Test MediaPipe functionality."""
    print("\n🎭 Testing MediaPipe...")
    
    try:
        import mediapipe as mp
        
        # Test face mesh
        face_mesh = mp.solutions.face_mesh.FaceMesh(
            static_image_mode=True,
            max_num_faces=1,
            refine_landmarks=True,
            min_detection_confidence=0.5
        )
        print("✅ MediaPipe Face Mesh initialized")
        
        # Test pose
        pose = mp.solutions.pose.Pose(
            static_image_mode=True,
            model_complexity=2,
            enable_segmentation=True,
            min_detection_confidence=0.5
        )
        print("✅ MediaPipe Pose initialized")
        
        # Test selfie segmentation
        selfie_seg = mp.solutions.selfie_segmentation.SelfieSegmentation(
            model_selection=1
        )
        print("✅ MediaPipe Selfie Segmentation initialized")
        
        return True
        
    except Exception as e:
        print(f"❌ MediaPipe test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 Pixar Character Generator - Installation Test")
    print("=" * 60)
    
    tests = [
        ("Python Version", test_python_version),
        ("Dependencies", test_dependencies),
        ("Project Structure", test_project_structure),
        ("Configuration", test_configuration),
        ("Module Imports", test_imports),
        ("MediaPipe", test_mediapipe)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Your installation is ready.")
        print("\nNext steps:")
        print("1. Run 'python demo.py' for a quick demo")
        print("2. Run 'python main.py' to start the web interface")
        print("3. Add face images to the 'examples' directory")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please fix the issues above.")
        print("\nTroubleshooting:")
        print("1. Install missing dependencies: pip install -r requirements.txt")
        print("2. Make sure you're in the project root directory")
        print("3. Check Python version (requires 3.8+)")

if __name__ == "__main__":
    main()
