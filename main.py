#!/usr/bin/env python3
"""
Pixar Style 3D Character Generator
Main application entry point for converting face images to 3D Pixar-style characters.
"""

import os
import sys
import argparse
import logging
from pathlib import Path

import gradio as gr
import numpy as np
from PIL import Image

from src.face_analyzer import <PERSON><PERSON><PERSON>yzer
from src.face_3d_generator import Face3DGenerator
from src.pixar_stylizer import PixarStylizer
from src.clothing_pose_extractor import ClothingPoseExtractor
from src.background_processor import BackgroundProcessor
from src.renderer import PixarRenderer
from src.utils.config import Config
from src.utils.logger import setup_logger

# Setup logging
logger = setup_logger(__name__)

class PixarCharacterGenerator:
    """Main class for generating Pixar-style 3D characters from face images."""
    
    def __init__(self, config_path: str = "config/default.yaml"):
        """Initialize the generator with configuration."""
        self.config = Config(config_path)
        
        # Initialize all components
        self.face_analyzer = FaceAnalyzer(self.config)
        self.face_3d_generator = Face3DGenerator(self.config)
        self.pixar_stylizer = PixarStylizer(self.config)
        self.clothing_extractor = ClothingPoseExtractor(self.config)
        self.background_processor = BackgroundProcessor(self.config)
        self.renderer = PixarRenderer(self.config)
        
        logger.info("Pixar Character Generator initialized successfully")
    
    def generate_character(self, input_image: np.ndarray, style_intensity: float = 0.8) -> np.ndarray:
        """
        Generate a 3D Pixar-style character from an input face image.
        
        Args:
            input_image: Input face image as numpy array
            style_intensity: Intensity of Pixar stylization (0.0 to 1.0)
            
        Returns:
            Generated character image as numpy array (1920x1080)
        """
        try:
            logger.info("Starting character generation process")
            
            # Step 1: Analyze face features
            logger.info("Analyzing face features...")
            face_data = self.face_analyzer.analyze(input_image)
            
            # Step 2: Generate 3D face model
            logger.info("Generating 3D face model...")
            face_3d_model = self.face_3d_generator.generate(face_data)
            
            # Step 3: Apply Pixar stylization
            logger.info("Applying Pixar stylization...")
            stylized_model = self.pixar_stylizer.stylize(face_3d_model, style_intensity)
            
            # Step 4: Extract clothing and pose information
            logger.info("Extracting clothing and pose...")
            clothing_pose_data = self.clothing_extractor.extract(input_image, face_data)
            
            # Step 5: Process background
            logger.info("Processing background...")
            background_data = self.background_processor.process(input_image, face_data)
            
            # Step 6: Render final character
            logger.info("Rendering final character...")
            final_image = self.renderer.render(
                stylized_model, 
                clothing_pose_data, 
                background_data,
                output_size=(1920, 1080)
            )
            
            logger.info("Character generation completed successfully")
            return final_image
            
        except Exception as e:
            logger.error(f"Error during character generation: {str(e)}")
            raise

def create_gradio_interface(generator: PixarCharacterGenerator):
    """Create Gradio web interface for the character generator."""
    
    def process_image(image, style_intensity):
        """Process uploaded image and return generated character."""
        if image is None:
            return None, "Please upload an image first."
        
        try:
            # Convert PIL image to numpy array
            input_array = np.array(image)
            
            # Generate character
            result = generator.generate_character(input_array, style_intensity)
            
            # Convert back to PIL for display
            result_image = Image.fromarray(result)
            
            return result_image, "Character generated successfully!"
            
        except Exception as e:
            return None, f"Error: {str(e)}"
    
    # Create interface
    with gr.Blocks(title="Pixar Character Generator") as interface:
        gr.Markdown("# 🎭 Pixar Style 3D Character Generator")
        gr.Markdown("Upload a face image to create a 3D Pixar-style character portrait!")
        
        with gr.Row():
            with gr.Column():
                input_image = gr.Image(
                    type="pil",
                    label="Upload Face Image",
                    height=400
                )
                style_intensity = gr.Slider(
                    minimum=0.0,
                    maximum=1.0,
                    value=0.8,
                    step=0.1,
                    label="Pixar Style Intensity"
                )
                generate_btn = gr.Button("Generate Character", variant="primary")
                
            with gr.Column():
                output_image = gr.Image(
                    label="Generated Pixar Character",
                    height=400
                )
                status_text = gr.Textbox(
                    label="Status",
                    interactive=False
                )
        
        generate_btn.click(
            fn=process_image,
            inputs=[input_image, style_intensity],
            outputs=[output_image, status_text]
        )
    
    return interface

def main():
    """Main function to run the application."""
    parser = argparse.ArgumentParser(description="Pixar Style 3D Character Generator")
    parser.add_argument("--config", default="config/default.yaml", help="Configuration file path")
    parser.add_argument("--port", type=int, default=7860, help="Port for web interface")
    parser.add_argument("--share", action="store_true", help="Create shareable link")
    
    args = parser.parse_args()
    
    try:
        # Initialize generator
        generator = PixarCharacterGenerator(args.config)
        
        # Create and launch interface
        interface = create_gradio_interface(generator)
        interface.launch(
            server_port=args.port,
            share=args.share,
            inbrowser=True
        )
        
    except Exception as e:
        logger.error(f"Failed to start application: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
