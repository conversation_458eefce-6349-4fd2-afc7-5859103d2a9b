"""
Clothing and pose extraction module for preserving clothing details and body pose.
"""

import cv2
import numpy as np
import mediapipe as mp
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass

from .face_analyzer import FaceData
from .utils.logger import get_logger

logger = get_logger(__name__)

@dataclass
class ClothingData:
    """Container for clothing information."""
    clothing_mask: np.ndarray  # Binary mask for clothing regions
    clothing_colors: Dict[str, Tuple[int, int, int]]  # Dominant colors
    clothing_types: List[str]  # Detected clothing types
    texture_regions: Dict[str, np.ndarray]  # Texture information

@dataclass
class PoseData:
    """Container for pose information."""
    body_landmarks: np.ndarray  # Body pose landmarks
    shoulder_angle: float  # Shoulder rotation angle
    head_tilt: float  # Head tilt angle
    body_orientation: str  # front, side, etc.
    pose_confidence: float  # Detection confidence

@dataclass
class ClothingPoseData:
    """Combined clothing and pose data."""
    clothing: ClothingData
    pose: PoseData
    body_bbox: Tuple[int, int, int, int]  # Body bounding box

class ClothingPoseExtractor:
    """Extract clothing details and pose information from images."""
    
    def __init__(self, config):
        """
        Initialize the clothing and pose extractor.
        
        Args:
            config: Configuration object
        """
        self.config = config
        self.clothing_categories = config.get('clothing_pose.clothing_categories', 
                                            ["shirt", "jacket", "dress", "accessories"])
        
        # Initialize MediaPipe Pose
        self.mp_pose = mp.solutions.pose
        self.pose = self.mp_pose.Pose(
            static_image_mode=True,
            model_complexity=2,
            enable_segmentation=True,
            min_detection_confidence=0.7
        )
        
        # Initialize MediaPipe Selfie Segmentation for clothing
        self.mp_selfie_segmentation = mp.solutions.selfie_segmentation
        self.selfie_segmentation = self.mp_selfie_segmentation.SelfieSegmentation(
            model_selection=1
        )
        
        logger.info("Clothing and pose extractor initialized")
    
    def extract(self, image: np.ndarray, face_data: FaceData) -> ClothingPoseData:
        """
        Extract clothing and pose information from image.
        
        Args:
            image: Input image as numpy array
            face_data: Face analysis data for context
            
        Returns:
            ClothingPoseData object
        """
        # Convert BGR to RGB if needed
        if len(image.shape) == 3 and image.shape[2] == 3:
            rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        else:
            rgb_image = image
        
        # Extract pose information
        pose_data = self._extract_pose(rgb_image, face_data)
        
        # Extract clothing information
        clothing_data = self._extract_clothing(rgb_image, pose_data)
        
        # Calculate body bounding box
        body_bbox = self._calculate_body_bbox(pose_data, face_data)
        
        return ClothingPoseData(
            clothing=clothing_data,
            pose=pose_data,
            body_bbox=body_bbox
        )
    
    def _extract_pose(self, image: np.ndarray, face_data: FaceData) -> PoseData:
        """Extract pose information using MediaPipe."""
        results = self.pose.process(image)
        
        if not results.pose_landmarks:
            # Create default pose data if detection fails
            return self._create_default_pose(face_data)
        
        # Extract landmarks
        landmarks = results.pose_landmarks.landmark
        h, w = image.shape[:2]
        
        body_landmarks = []
        for landmark in landmarks:
            x = int(landmark.x * w)
            y = int(landmark.y * h)
            z = landmark.z
            visibility = landmark.visibility
            body_landmarks.append([x, y, z, visibility])
        
        body_landmarks = np.array(body_landmarks, dtype=np.float32)
        
        # Calculate pose parameters
        shoulder_angle = self._calculate_shoulder_angle(body_landmarks)
        head_tilt = self._calculate_head_tilt(body_landmarks, face_data)
        body_orientation = self._determine_body_orientation(body_landmarks)
        pose_confidence = self._calculate_pose_confidence(body_landmarks)
        
        return PoseData(
            body_landmarks=body_landmarks,
            shoulder_angle=shoulder_angle,
            head_tilt=head_tilt,
            body_orientation=body_orientation,
            pose_confidence=pose_confidence
        )
    
    def _extract_clothing(self, image: np.ndarray, pose_data: PoseData) -> ClothingData:
        """Extract clothing information from image."""
        # Get person segmentation mask
        results = self.selfie_segmentation.process(image)
        person_mask = results.segmentation_mask
        
        # Convert to binary mask
        clothing_mask = (person_mask > 0.5).astype(np.uint8)
        
        # Remove face region from clothing mask
        clothing_mask = self._remove_face_region(clothing_mask, pose_data)
        
        # Extract clothing colors
        clothing_colors = self._extract_clothing_colors(image, clothing_mask)
        
        # Detect clothing types (simplified)
        clothing_types = self._detect_clothing_types(image, clothing_mask, pose_data)
        
        # Extract texture regions
        texture_regions = self._extract_texture_regions(image, clothing_mask)
        
        return ClothingData(
            clothing_mask=clothing_mask,
            clothing_colors=clothing_colors,
            clothing_types=clothing_types,
            texture_regions=texture_regions
        )
    
    def _create_default_pose(self, face_data: FaceData) -> PoseData:
        """Create default pose data when pose detection fails."""
        # Use face pose as reference
        face_pose = face_data.face_pose
        
        # Create minimal body landmarks (shoulders, etc.)
        face_center = np.mean(face_data.landmarks_2d, axis=0)
        
        # Estimate shoulder positions based on face
        shoulder_width = face_data.face_bbox[2] * 1.5
        shoulder_y = face_center[1] + face_data.face_bbox[3] * 0.8
        
        body_landmarks = np.array([
            [face_center[0] - shoulder_width/2, shoulder_y, 0, 0.8],  # Left shoulder
            [face_center[0] + shoulder_width/2, shoulder_y, 0, 0.8],  # Right shoulder
            [face_center[0], face_center[1], 0, 0.9],  # Nose (reference)
        ])
        
        return PoseData(
            body_landmarks=body_landmarks,
            shoulder_angle=face_pose['roll'],
            head_tilt=face_pose['pitch'],
            body_orientation="front",
            pose_confidence=0.5
        )
    
    def _calculate_shoulder_angle(self, landmarks: np.ndarray) -> float:
        """Calculate shoulder rotation angle."""
        # MediaPipe pose landmark indices
        left_shoulder_idx = 11
        right_shoulder_idx = 12
        
        if len(landmarks) > max(left_shoulder_idx, right_shoulder_idx):
            left_shoulder = landmarks[left_shoulder_idx][:2]
            right_shoulder = landmarks[right_shoulder_idx][:2]
            
            # Calculate angle
            shoulder_vector = right_shoulder - left_shoulder
            angle = np.arctan2(shoulder_vector[1], shoulder_vector[0])
            return np.degrees(angle)
        
        return 0.0
    
    def _calculate_head_tilt(self, landmarks: np.ndarray, face_data: FaceData) -> float:
        """Calculate head tilt angle."""
        # Use face pose data as primary source
        return face_data.face_pose['pitch']
    
    def _determine_body_orientation(self, landmarks: np.ndarray) -> str:
        """Determine body orientation (front, side, etc.)."""
        # Simplified orientation detection
        left_shoulder_idx = 11
        right_shoulder_idx = 12
        
        if len(landmarks) > max(left_shoulder_idx, right_shoulder_idx):
            left_shoulder = landmarks[left_shoulder_idx]
            right_shoulder = landmarks[right_shoulder_idx]
            
            # Check visibility to determine orientation
            left_vis = left_shoulder[3] if len(left_shoulder) > 3 else 1.0
            right_vis = right_shoulder[3] if len(right_shoulder) > 3 else 1.0
            
            if left_vis > 0.7 and right_vis > 0.7:
                return "front"
            elif left_vis > right_vis:
                return "left_side"
            else:
                return "right_side"
        
        return "front"
    
    def _calculate_pose_confidence(self, landmarks: np.ndarray) -> float:
        """Calculate overall pose detection confidence."""
        if len(landmarks) == 0:
            return 0.0
        
        # Average visibility scores
        visibilities = landmarks[:, 3] if landmarks.shape[1] > 3 else np.ones(len(landmarks))
        return float(np.mean(visibilities))
    
    def _remove_face_region(self, clothing_mask: np.ndarray, pose_data: PoseData) -> np.ndarray:
        """Remove face region from clothing mask."""
        mask = clothing_mask.copy()
        
        # Estimate face region from pose landmarks
        if len(pose_data.body_landmarks) > 0:
            # Use nose landmark as reference
            nose_idx = 0  # Nose landmark
            if len(pose_data.body_landmarks) > nose_idx:
                nose_pos = pose_data.body_landmarks[nose_idx][:2].astype(int)
                
                # Create circular mask around face
                face_radius = 80  # Approximate face radius
                y, x = np.ogrid[:mask.shape[0], :mask.shape[1]]
                face_mask = (x - nose_pos[0])**2 + (y - nose_pos[1])**2 <= face_radius**2
                
                # Remove face region
                mask[face_mask] = 0
        
        return mask
    
    def _extract_clothing_colors(self, image: np.ndarray, clothing_mask: np.ndarray) -> Dict[str, Tuple[int, int, int]]:
        """Extract dominant colors from clothing regions."""
        colors = {}
        
        # Get clothing pixels
        clothing_pixels = image[clothing_mask > 0]
        
        if len(clothing_pixels) > 0:
            # Calculate dominant color using k-means (simplified)
            # For now, use mean color
            mean_color = np.mean(clothing_pixels, axis=0).astype(int)
            colors['primary'] = tuple(mean_color)
            
            # Calculate secondary color (simplified)
            # Use median for variation
            median_color = np.median(clothing_pixels, axis=0).astype(int)
            colors['secondary'] = tuple(median_color)
        else:
            colors['primary'] = (128, 128, 128)  # Default gray
            colors['secondary'] = (100, 100, 100)
        
        return colors
    
    def _detect_clothing_types(self, image: np.ndarray, clothing_mask: np.ndarray, pose_data: PoseData) -> List[str]:
        """Detect types of clothing (simplified detection)."""
        clothing_types = []
        
        # Analyze clothing mask regions
        h, w = clothing_mask.shape
        
        # Check for upper body clothing
        upper_region = clothing_mask[:h//2, :]
        if np.sum(upper_region) > 1000:  # Threshold for clothing presence
            clothing_types.append("shirt")
        
        # Check for lower body clothing
        lower_region = clothing_mask[h//2:, :]
        if np.sum(lower_region) > 500:
            clothing_types.append("pants")
        
        # Default to shirt if nothing detected
        if not clothing_types:
            clothing_types.append("shirt")
        
        return clothing_types
    
    def _extract_texture_regions(self, image: np.ndarray, clothing_mask: np.ndarray) -> Dict[str, np.ndarray]:
        """Extract texture information from clothing regions."""
        texture_regions = {}
        
        # Extract clothing region
        clothing_region = image * clothing_mask[:, :, np.newaxis]
        
        # Calculate texture features (simplified)
        # Use gradient magnitude as texture measure
        gray_clothing = cv2.cvtColor(clothing_region, cv2.COLOR_RGB2GRAY)
        
        # Calculate gradients
        grad_x = cv2.Sobel(gray_clothing, cv2.CV_64F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(gray_clothing, cv2.CV_64F, 0, 1, ksize=3)
        texture_magnitude = np.sqrt(grad_x**2 + grad_y**2)
        
        texture_regions['texture_map'] = texture_magnitude
        texture_regions['clothing_region'] = clothing_region
        
        return texture_regions
    
    def _calculate_body_bbox(self, pose_data: PoseData, face_data: FaceData) -> Tuple[int, int, int, int]:
        """Calculate bounding box for the entire body."""
        if len(pose_data.body_landmarks) > 0:
            # Use pose landmarks to calculate bbox
            landmarks = pose_data.body_landmarks[:, :2]  # x, y coordinates
            
            x_min = int(np.min(landmarks[:, 0]))
            x_max = int(np.max(landmarks[:, 0]))
            y_min = int(np.min(landmarks[:, 1]))
            y_max = int(np.max(landmarks[:, 1]))
            
            # Add some padding
            padding = 20
            x_min = max(0, x_min - padding)
            y_min = max(0, y_min - padding)
            width = x_max - x_min + 2 * padding
            height = y_max - y_min + 2 * padding
            
            return (x_min, y_min, width, height)
        else:
            # Fallback to face-based estimation
            face_bbox = face_data.face_bbox
            body_width = int(face_bbox[2] * 2.5)
            body_height = int(face_bbox[3] * 4)
            body_x = face_bbox[0] - (body_width - face_bbox[2]) // 2
            body_y = face_bbox[1]
            
            return (body_x, body_y, body_width, body_height)
